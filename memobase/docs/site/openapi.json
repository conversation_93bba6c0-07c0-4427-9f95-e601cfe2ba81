{"openapi": "3.1.0", "info": {"title": "Memobase API", "summary": "APIs for Memobase, a user memory system for LLM Apps", "version": "0.0.33"}, "servers": [{"url": "https://api.memobase.dev"}, {"url": "https://api.memobase.cn"}], "paths": {"/api/v1/healthcheck": {"get": {"tags": ["chore"], "summary": "Healthcheck", "description": "Check if your memobase is set up correctly", "operationId": "healthcheck_api_v1_healthcheck_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponse"}}}}}, "x-code-samples": [{"lang": "Python", "source": "# To use the Python SDK, install the package:\n# pip install memobase\n\nfrom memobase import Memobase\n\nmemobase = Memobase(project_url='PROJECT_URL', api_key='PROJECT_TOKEN')\n\nassert memobase.ping()\n", "label": "Python"}, {"lang": "JavaScript", "source": "// To use the JavaScript SDK, install the package:\n// npm install @memobase/memobase\n\nimport { MemoBaseClient } from '@memobase/memobase';\n\nconst client = new MemoBaseClient(process.env.MEMOBASE_PROJECT_URL, process.env.MEMOBASE_API_KEY);\n\nawait client.ping();\n", "label": "JavaScript"}, {"lang": "Go", "source": "// To use the Go SDK, install the package:\n// go get github.com/memodb-io/memobase/src/client/memobase-go@latest\n\nimport (\n    \"github.com/memodb-io/memobase/src/client/memobase-go/core\"\n)\n\nprojectURL := \"YOUR_PROJECT_URL\"\napiKey := \"YOUR_API_KEY\"\nclient, err := core.NewMemoBaseClient(projectURL, apiKey)\nif err != nil {\n    panic(err)\n}\n\nok := client.Ping()\nif !ok {\n    panic(\"Failed to connect to Memobase\")\n}\n", "label": "Go"}], "security": []}}, "/api/v1/project/profile_config": {"get": {"tags": ["project"], "summary": "Get Project Profile Config String", "operationId": "get_project_profile_config_string_api_v1_project_profile_config_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProfileConfigDataResponse"}}}}}, "x-code-samples": [{"lang": "Python", "source": "# To use the Python SDK, install the package:\n# pip install memobase\n\nfrom memobase import Memobase\n\nmemobase = Memobase(project_url='PROJECT_URL', api_key='PROJECT_TOKEN')\n\nconfig = memobase.get_config()\n", "label": "Python"}, {"lang": "JavaScript", "source": "// To use the JavaScript SDK, install the package:\n// npm install @memobase/memobase\n\nimport { MemoBaseClient } from '@memobase/memobase';\n\nconst client = new MemoBaseClient(process.env.MEMOBASE_PROJECT_URL, process.env.MEMOBASE_API_KEY);\n\nconst config = await client.getConfig();\n", "label": "JavaScript"}]}, "post": {"tags": ["project"], "summary": "Update Project Profile Config", "operationId": "update_project_profile_config_api_v1_project_profile_config_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProfileConfigData", "description": "The profile config to update"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "x-code-samples": [{"lang": "Python", "source": "# To use the Python SDK, install the package:\n# pip install memobase\n\nfrom memobase import Memobase\n\nmemobase = Memobase(project_url='PROJECT_URL', api_key='PROJECT_TOKEN')\n\nmemobase.update_config('your_profile_config')\n", "label": "Python"}, {"lang": "JavaScript", "source": "// To use the JavaScript SDK, install the package:\n// npm install @memobase/memobase\n\nimport { MemoBaseClient } from '@memobase/memobase';\n\nconst client = new MemoBaseClient(process.env.MEMOBASE_PROJECT_URL, process.env.MEMOBASE_API_KEY);\n\nawait client.updateConfig('your_profile_config');\n", "label": "JavaScript"}]}}, "/api/v1/project/billing": {"get": {"tags": ["project"], "summary": "Get Project Billing", "operationId": "get_project_billing_api_v1_project_billing_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BillingResponse"}}}}}, "x-code-samples": [{"lang": "Python", "source": "# To use the Python SDK, install the package:\n# pip install memobase\n\nfrom memobase import Memobase\n\nmemobase = Memobase(project_url='PROJECT_URL', api_key='PROJECT_TOKEN')\n\nprint(memobase.get_usage())\n", "label": "Python"}]}}, "/api/v1/users": {"post": {"tags": ["user"], "summary": "Create User", "description": "Create a new user with additional data", "operationId": "create_user_api_v1_users_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserData", "description": "User data for creating a new user"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/IdResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "x-code-samples": [{"lang": "Python", "source": "# To use the Python SDK, install the package:\n# pip install memobase\n\nfrom memobase import Memobase\n\nclient = Memobase(project_url='PROJECT_URL', api_key='PROJECT_TOKEN')\n\nuid = client.add_user({\"ANY\": \"DATA\"})\n", "label": "Python"}, {"lang": "JavaScript", "source": "// To use the JavaScript SDK, install the package:\n// npm install @memobase/memobase\n\nimport { MemoBaseClient } from '@memobase/memobase';\n\nconst client = new MemoBaseClient(process.env.MEMOBASE_PROJECT_URL, process.env.MEMOBASE_API_KEY);\n\nconst userId = await client.addUser({ANY: \"DATA\"});\n", "label": "JavaScript"}, {"lang": "Go", "source": "// To use the Go SDK, install the package:\n// go get github.com/memodb-io/memobase/src/client/memobase-go@latest\n\nimport (\n    \"github.com/memodb-io/memobase/src/client/memobase-go/core\"\n    \"github.com/google/uuid\"\n)\n\nprojectURL := \"YOUR_PROJECT_URL\"\napiKey := \"YOUR_API_KEY\"\nclient, err := core.NewMemoBaseClient(projectURL, apiKey)\nif err != nil {\n    panic(err)\n}\n\n// Generate a UUID for the user\nuserID := uuid.New().String()\n\n// Create user with some data\ndata := map[string]interface{}{\"ANY\": \"DATA\"}\nresultID, err := client.AddUser(data, userID)\nif err != nil {\n    panic(err)\n}\n", "label": "Go"}]}}, "/api/v1/users/{user_id}": {"get": {"tags": ["user"], "summary": "Get User", "operationId": "get_user_api_v1_users__user_id__get", "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "string", "description": "The ID of the user to retrieve", "title": "User Id"}, "description": "The ID of the user to retrieve"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserDataResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "x-code-samples": [{"lang": "Python", "source": "# To use the Python SDK, install the package:\n# pip install memobase\n\nfrom memobase import Memobase\n\nclient = Memobase(project_url='PROJECT_URL', api_key='PROJECT_TOKEN')\n\nu = client.get_user(uid)\n", "label": "Python"}, {"lang": "JavaScript", "source": "// To use the JavaScript SDK, install the package:\n// npm install @memobase/memobase\n\nimport { MemoBaseClient } from '@memobase/memobase';\n\nconst client = new MemoBaseClient(process.env.MEMOBASE_PROJECT_URL, process.env.MEMOBASE_API_KEY);\n\nconst user = await client.getUser(userId);\n", "label": "JavaScript"}, {"lang": "Go", "source": "// To use the Go SDK, install the package:\n// go get github.com/memodb-io/memobase/src/client/memobase-go@latest\n\nimport (\n    \"github.com/memodb-io/memobase/src/client/memobase-go/core\"\n)\n\nprojectURL := \"YOUR_PROJECT_URL\"\napiKey := \"YOUR_API_KEY\"\nclient, err := core.NewMemoBaseClient(projectURL, apiKey)\nif err != nil {\n    panic(err)\n}\n\n// Get user by ID\nuser, err := client.GetUser(userID)\nif err != nil {\n    panic(err)\n}\n", "label": "Go"}]}, "put": {"tags": ["user"], "summary": "Update User", "operationId": "update_user_api_v1_users__user_id__put", "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "string", "description": "The ID of the user to update", "title": "User Id"}, "description": "The ID of the user to update"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true, "description": "Updated user data", "title": "User Data"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/IdResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "x-code-samples": [{"lang": "Python", "source": "# To use the Python SDK, install the package:\n# pip install memobase\n\nfrom memobase import Memobase\n\nclient = Memobase(project_url='PROJECT_URL', api_key='PROJECT_TOKEN')\n\nclient.update_user(uid, {\"ANY\": \"NEW_DATA\"})\n", "label": "Python"}, {"lang": "JavaScript", "source": "// To use the JavaScript SDK, install the package:\n// npm install @memobase/memobase\n\nimport { MemoBaseClient } from '@memobase/memobase';\n\nconst client = new MemoBaseClient(process.env.MEMOBASE_PROJECT_URL, process.env.MEMOBASE_API_KEY);\n\nawait client.updateUser(userId, {ANY: \"NEW_DATA\"});\n", "label": "JavaScript"}, {"lang": "Go", "source": "// To use the Go SDK, install the package:\n// go get github.com/memodb-io/memobase/src/client/memobase-go@latest\n\nimport (\n    \"github.com/memodb-io/memobase/src/client/memobase-go/core\"\n)\n\nprojectURL := \"YOUR_PROJECT_URL\"\napiKey := \"YOUR_API_KEY\"\nclient, err := core.NewMemoBaseClient(projectURL, apiKey)\nif err != nil {\n    panic(err)\n}\n\n// Update user data\nnewData := map[string]interface{}{\"ANY\": \"NEW_DATA\"}\nerr = client.UpdateUser(userID, newData)\nif err != nil {\n    panic(err)\n}\n", "label": "Go"}]}, "delete": {"tags": ["user"], "summary": "Delete User", "operationId": "delete_user_api_v1_users__user_id__delete", "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "string", "description": "The ID of the user to delete", "title": "User Id"}, "description": "The ID of the user to delete"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "x-code-samples": [{"lang": "Python", "source": "# To use the Python SDK, install the package:\n# pip install memobase\n\nfrom memobase import Memobase\n\nclient = Memobase(project_url='PROJECT_URL', api_key='PROJECT_TOKEN')\n\nclient.delete_user(uid)\n", "label": "Python"}, {"lang": "JavaScript", "source": "// To use the JavaScript SDK, install the package:\n// npm install @memobase/memobase\n\nimport { MemoBaseClient } from '@memobase/memobase';\n\nconst client = new MemoBaseClient(process.env.MEMOBASE_PROJECT_URL, process.env.MEMOBASE_API_KEY);\n\nawait client.deleteUser(userId);\n", "label": "JavaScript"}, {"lang": "Go", "source": "// To use the Go SDK, install the package:\n// go get github.com/memodb-io/memobase/src/client/memobase-go@latest\n\nimport (\n    \"github.com/memodb-io/memobase/src/client/memobase-go/core\"\n)\n\nprojectURL := \"YOUR_PROJECT_URL\"\napiKey := \"YOUR_API_KEY\"\nclient, err := core.NewMemoBaseClient(projectURL, apiKey)\nif err != nil {\n    panic(err)\n}\n\n// Delete user\nerr = client.DeleteUser(userID)\nif err != nil {\n    panic(err)\n}\n", "label": "Go"}]}}, "/api/v1/users/blobs/{user_id}/{blob_type}": {"get": {"tags": ["user"], "summary": "Get User All Blobs", "operationId": "get_user_all_blobs_api_v1_users_blobs__user_id___blob_type__get", "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "string", "description": "The ID of the user to fetch blobs for", "title": "User Id"}, "description": "The ID of the user to fetch blobs for"}, {"name": "blob_type", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/BlobType", "description": "The type of blobs to retrieve"}, "description": "The type of blobs to retrieve"}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "description": "Page number for pagination, starting from 0", "default": 0, "title": "Page"}, "description": "Page number for pagination, starting from 0"}, {"name": "page_size", "in": "query", "required": false, "schema": {"type": "integer", "description": "Number of items per page, default is 10", "default": 10, "title": "<PERSON>"}, "description": "Number of items per page, default is 10"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/IdsResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "x-code-samples": [{"lang": "Python", "source": "# To use the Python SDK, install the package:\n# pip install memobase\n\nfrom memobase import Memobase\nfrom memobase.core.blob import BlobType\n\nmemobase = Memobase(project_url='PROJECT_URL', api_key='PROJECT_TOKEN')\n\nuser = memobase.get_user('user_id')\nblobs = user.get_all(BlobType.CHAT)\n", "label": "Python"}, {"lang": "JavaScript", "source": "// To use the JavaScript SDK, install the package:\n// npm install @memobase/memobase\n\nimport { MemoBaseClient, BlobType } from '@memobase/memobase';\n\nconst client = new MemoBaseClient(process.env.MEMOBASE_PROJECT_URL, process.env.MEMOBASE_API_KEY);\n\nconst user = client.getUser('user_id');\nconst blobs = await user.getAll(BlobType.Enum.chat);\n", "label": "JavaScript"}, {"lang": "Go", "source": "// To use the Go SDK, install the package:\n// go get github.com/memodb-io/memobase/src/client/memobase-go@latest\n\nimport (\n    \"github.com/memodb-io/memobase/src/client/memobase-go/core\"\n    \"github.com/memodb-io/memobase/src/client/memobase-go/blob\"\n)\n\nprojectURL := \"YOUR_PROJECT_URL\"\napiKey := \"YOUR_API_KEY\"\nclient, err := core.NewMemoBaseClient(projectURL, apiKey)\nif err != nil {\n    panic(err)\n}\n\n// Get user\nuser, err := client.GetUser(userID)\nif err != nil {\n    panic(err)\n}\n\n// Get all blobs\nblobs, err := user.GetAll(blob.ChatType)\nif err != nil {\n    panic(err)\n}\n", "label": "Go"}]}}, "/api/v1/blobs/insert/{user_id}": {"post": {"tags": ["blob"], "summary": "Insert Blob", "operationId": "insert_blob_api_v1_blobs_insert__user_id__post", "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "string", "description": "The ID of the user to insert the blob for", "title": "User Id"}, "description": "The ID of the user to insert the blob for"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlobData", "description": "The blob data to insert"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlobInsertResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "x-code-samples": [{"lang": "Python", "source": "# To use the Python SDK, install the package:\n# pip install memobase\n\nfrom memobase import Memobase\nfrom memobase import ChatBlob\n\nclient = Memobase(project_url='PROJECT_URL', api_key='PROJECT_TOKEN')\n\nb = ChatBlob(messages=[\n    {\n        \"role\": \"user\",\n        \"content\": \"Hi, I'm here again\"\n    },\n    {\n        \"role\": \"assistant\",\n        \"content\": \"<PERSON>, <PERSON>! How can I help you?\"\n    }\n])\nu = client.get_user(uid)\nbid = u.insert(b)\n", "label": "Python"}, {"lang": "JavaScript", "source": "// To use the JavaScript SDK, install the package:\n// npm install @memobase/memobase\n\nimport { MemoBaseClient, Blob, BlobType } from '@memobase/memobase';\n\nconst client = new MemoBaseClient(process.env.MEMOBASE_PROJECT_URL, process.env.MEMOBASE_API_KEY);\nconst user = await client.getUser(userId);\n\nconst blobId = await user.insert(Blob.parse({\n  type: BlobType.Enum.chat,\n  messages: [\n    {\n      role: 'user',\n      content: 'Hi, I'm here again'\n    },\n    {\n      role: 'assistant',\n      content: 'Hi, <PERSON>! How can I help you?'\n    }\n  ]\n}));\n", "label": "JavaScript"}, {"lang": "Go", "source": "// To use the Go SDK, install the package:\n// go get github.com/memodb-io/memobase/src/client/memobase-go@latest\n\nimport (\n    \"github.com/memodb-io/memobase/src/client/memobase-go/core\"\n    \"github.com/memodb-io/memobase/src/client/memobase-go/blob\"\n)\n\nprojectURL := \"YOUR_PROJECT_URL\"\napiKey := \"YOUR_API_KEY\"\nclient, err := core.NewMemoBaseClient(projectURL, apiKey)\nif err != nil {\n    panic(err)\n}\n\n// Get user\nuser, err := client.GetUser(userID)\nif err != nil {\n    panic(err)\n}\n\n// Create chat blob\nchatBlob := &blob.ChatBlob{\n    BaseBlob: blob.BaseBlob{\n        Type: blob.ChatType,\n    },\n    Messages: []blob.OpenAICompatibleMessage{\n        {\n            Role:    \"user\",\n            Content: \"Hi, I'm here again\",\n        },\n        {\n            Role:    \"assistant\",\n            Content: \"Hi, <PERSON>! How can I help you?\",\n        },\n    },\n}\n\n// Insert blob\nblobID, err := user.Insert(chatBlob)\nif err != nil {\n    panic(err)\n}\n", "label": "Go"}]}}, "/api/v1/blobs/{user_id}/{blob_id}": {"get": {"tags": ["blob"], "summary": "Get Blob", "operationId": "get_blob_api_v1_blobs__user_id___blob_id__get", "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "string", "description": "The ID of the user", "title": "User Id"}, "description": "The ID of the user"}, {"name": "blob_id", "in": "path", "required": true, "schema": {"type": "string", "description": "The ID of the blob to retrieve", "title": "Blob Id"}, "description": "The ID of the blob to retrieve"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlobDataResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "x-code-samples": [{"lang": "Python", "source": "# To use the Python SDK, install the package:\n# pip install memobase\n\nfrom memobase import Memobase\n\nclient = Memobase(project_url='PROJECT_URL', api_key='PROJECT_TOKEN')\n\nu = client.get_user(uid)\nb = u.get(bid)\n", "label": "Python"}, {"lang": "JavaScript", "source": "// To use the JavaScript SDK, install the package:\n// npm install @memobase/memobase\n\nimport { MemoBaseClient } from '@memobase/memobase';\n\nconst client = new MemoBaseClient(process.env.MEMOBASE_PROJECT_URL, process.env.MEMOBASE_API_KEY);\nconst user = await client.getUser(userId);\n\nconst blob = await user.get(blobId);\n", "label": "JavaScript"}, {"lang": "Go", "source": "// To use the Go SDK, install the package:\n// go get github.com/memodb-io/memobase/src/client/memobase-go@latest\n\nimport (\n    \"github.com/memodb-io/memobase/src/client/memobase-go/core\"\n    \"github.com/memodb-io/memobase/src/client/memobase-go/blob\"\n)\n\nprojectURL := \"YOUR_PROJECT_URL\"\napiKey := \"YOUR_API_KEY\"\nclient, err := core.NewMemoBaseClient(projectURL, apiKey)\nif err != nil {\n    panic(err)\n}\n\n// Get user\nuser, err := client.GetUser(userID)\nif err != nil {\n    panic(err)\n}\n\n// Get blob\nblob, err := user.Get(blobID)\nif err != nil {\n    panic(err)\n}\n\n// If it's a chat blob, you can access its messages\nif chatBlob, ok := blob.(*blob.ChatBlob); ok {\n    messages := chatBlob.Messages\n    // Process messages\n}\n", "label": "Go"}]}, "delete": {"tags": ["blob"], "summary": "Delete Blob", "operationId": "delete_blob_api_v1_blobs__user_id___blob_id__delete", "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "string", "description": "The ID of the user", "title": "User Id"}, "description": "The ID of the user"}, {"name": "blob_id", "in": "path", "required": true, "schema": {"type": "string", "description": "The ID of the blob to delete", "title": "Blob Id"}, "description": "The ID of the blob to delete"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "x-code-samples": [{"lang": "Python", "source": "# To use the Python SDK, install the package:\n# pip install memobase\n\nfrom memobase import Memobase\n\nclient = Memobase(project_url='PROJECT_URL', api_key='PROJECT_TOKEN')\n\nu = client.get_user(uid)\nu.delete(bid)\n", "label": "Python"}, {"lang": "JavaScript", "source": "// To use the JavaScript SDK, install the package:\n// npm install @memobase/memobase\n\nimport { MemoBaseClient } from '@memobase/memobase';\n\nconst client = new MemoBaseClient(process.env.MEMOBASE_PROJECT_URL, process.env.MEMOBASE_API_KEY);\nconst user = await client.getUser(userId);\n\nawait user.delete(blobId);\n", "label": "JavaScript"}, {"lang": "Go", "source": "// To use the Go SDK, install the package:\n// go get github.com/memodb-io/memobase/src/client/memobase-go@latest\n\nimport (\n    \"github.com/memodb-io/memobase/src/client/memobase-go/core\"\n)\n\nprojectURL := \"YOUR_PROJECT_URL\"\napiKey := \"YOUR_API_KEY\"\nclient, err := core.NewMemoBaseClient(projectURL, apiKey)\nif err != nil {\n    panic(err)\n}\n\n// Get user\nuser, err := client.GetUser(userID)\nif err != nil {\n    panic(err)\n}\n\n// Delete blob\nerr = user.Delete(blobID)\nif err != nil {\n    panic(err)\n}\n", "label": "Go"}]}}, "/api/v1/users/profile/{user_id}": {"get": {"tags": ["profile"], "summary": "Get User Profile", "description": "Get the real-time user profiles for long term memory", "operationId": "get_user_profile_api_v1_users_profile__user_id__get", "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "string", "description": "The ID of the user to get profiles for", "title": "User Id"}, "description": "The ID of the user to get profiles for"}, {"name": "topk", "in": "query", "required": false, "schema": {"type": "integer", "description": "Number of profiles to retrieve, default is all", "title": "Topk"}, "description": "Number of profiles to retrieve, default is all"}, {"name": "max_token_size", "in": "query", "required": false, "schema": {"type": "integer", "description": "Max token size of returned profile content, default is all", "title": "<PERSON>"}, "description": "Max token size of returned profile content, default is all"}, {"name": "prefer_topics", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}, "description": "Rank prefer topics at first to try to keep them in filtering, default order is by updated time", "title": "Prefer Topics"}, "description": "Rank prefer topics at first to try to keep them in filtering, default order is by updated time"}, {"name": "only_topics", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}, "description": "Only return profiles with these topics, default is all", "title": "Only Topics"}, "description": "Only return profiles with these topics, default is all"}, {"name": "max_subtopic_size", "in": "query", "required": false, "schema": {"type": "integer", "description": "Max subtopic size of the same topic in returned profile, default is all", "title": "Max Subtopic Size"}, "description": "Max subtopic size of the same topic in returned profile, default is all"}, {"name": "topic_limits_json", "in": "query", "required": false, "schema": {"type": "string", "description": "Set specific subtopic limits for topics in JSON, for example {\"topic1\": 3, \"topic2\": 5}. The limits in this param will override `max_subtopic_size`.", "title": "Topic Limits Json"}, "description": "Set specific subtopic limits for topics in JSON, for example {\"topic1\": 3, \"topic2\": 5}. The limits in this param will override `max_subtopic_size`."}, {"name": "chats_str", "in": "query", "required": false, "schema": {"type": "string", "description": "List of chats in OpenAI Message format, for example: [{\"role\": \"user\", \"content\": \"Hello\"}, {\"role\": \"assistant\", \"content\": \"Hi\"}]", "title": "Chats Str"}, "description": "List of chats in OpenAI Message format, for example: [{\"role\": \"user\", \"content\": \"Hello\"}, {\"role\": \"assistant\", \"content\": \"Hi\"}]"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserProfileResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "x-code-samples": [{"lang": "Python", "source": "# To use the Python SDK, install the package:\n# pip install memobase\n\nfrom memobase import Memobase\n\nclient = Memobase(project_url='PROJECT_URL', api_key='PROJECT_TOKEN')\n\nu = client.get_user(uid)\np = u.profile()\n", "label": "Python"}, {"lang": "JavaScript", "source": "// To use the JavaScript SDK, install the package:\n// npm install @memobase/memobase\n\nimport { MemoBaseClient } from '@memobase/memobase';\n\nconst client = new MemoBaseClient(process.env.MEMOBASE_PROJECT_URL, process.env.MEMOBASE_API_KEY);\nconst user = await client.getUser(userId);\n\nconst profiles = await user.profile();\n", "label": "JavaScript"}, {"lang": "Go", "source": "// To use the Go SDK, install the package:\n// go get github.com/memodb-io/memobase/src/client/memobase-go@latest\n\nimport (\n    \"github.com/memodb-io/memobase/src/client/memobase-go/core\"\n)\n\nprojectURL := \"YOUR_PROJECT_URL\"\napiKey := \"YOUR_API_KEY\"\nclient, err := core.NewMemoBaseClient(projectURL, apiKey)\nif err != nil {\n    panic(err)\n}\n\n// Get user\nuser, err := client.GetUser(userID)\nif err != nil {\n    panic(err)\n}\n\n// Get profile\nprofiles, err := user.Profile()\nif err != nil {\n    panic(err)\n}\n", "label": "Go"}]}, "post": {"tags": ["profile"], "summary": "Add User Profile", "description": "Add the real-time user profiles for long term memory", "operationId": "add_user_profile_api_v1_users_profile__user_id__post", "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "string", "description": "The ID of the user", "title": "User Id"}, "description": "The ID of the user"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProfileDelta", "description": "The content of the profile to add"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/IdResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "x-code-samples": [{"lang": "Python", "source": "# To use the Python SDK, install the package:\n# pip install memobase\n\nfrom memobase import Memobase\n\nclient = Memobase(project_url='PROJECT_URL', api_key='PROJECT_TOKEN')\n\nprofile_id = u.add_profile(\"value\", \"topic\", \"sub_topic\")\n", "label": "Python"}]}}, "/api/v1/users/profile/import/{user_id}": {"post": {"tags": ["profile"], "summary": "Import User Context", "operationId": "import_user_context_api_v1_users_profile_import__user_id__post", "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "string", "description": "The ID of the user", "title": "User Id"}, "description": "The ID of the user"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserContextImport", "description": "The content of the user context to import"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/users/profile/{user_id}/{profile_id}": {"put": {"tags": ["profile"], "summary": "Update User Profile", "description": "Update the real-time user profiles for long term memory", "operationId": "update_user_profile_api_v1_users_profile__user_id___profile_id__put", "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "string", "description": "The ID of the user", "title": "User Id"}, "description": "The ID of the user"}, {"name": "profile_id", "in": "path", "required": true, "schema": {"type": "string", "description": "The ID of the profile to update", "title": "Profile Id"}, "description": "The ID of the profile to update"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProfileDelta", "description": "The content of the profile to update"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "x-code-samples": [{"lang": "Python", "source": "# To use the Python SDK, install the package:\n# pip install memobase\n\nfrom memobase import Memobase\n\nclient = Memobase(project_url='PROJECT_URL', api_key='PROJECT_TOKEN')\n\nprofile_id = u.add_profile(\"value\", \"topic\", \"sub_topic\")\nu.update_profile(profile_id, \"value2\", \"topic2\", \"sub_topic2\")\n", "label": "Python"}]}, "delete": {"tags": ["profile"], "summary": "Delete User Profile", "description": "Get the real-time user profiles for long term memory", "operationId": "delete_user_profile_api_v1_users_profile__user_id___profile_id__delete", "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "string", "description": "The ID of the user", "title": "User Id"}, "description": "The ID of the user"}, {"name": "profile_id", "in": "path", "required": true, "schema": {"type": "string", "description": "The ID of the profile to delete", "title": "Profile Id"}, "description": "The ID of the profile to delete"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "x-code-samples": [{"lang": "Python", "source": "# To use the Python SDK, install the package:\n# pip install memobase\n\nfrom memobase import Memobase\n\nmemobase = Memobase(project_url='PROJECT_URL', api_key='PROJECT_TOKEN')\n\nmemobase.delete_profile('user_id', 'profile_id')\n", "label": "Python"}, {"lang": "JavaScript", "source": "// To use the JavaScript SDK, install the package:\n// npm install @memobase/memobase\n\nimport { MemoBaseClient } from '@memobase/memobase';\n\nconst client = new MemoBaseClient(process.env.MEMOBASE_PROJECT_URL, process.env.MEMOBASE_API_KEY);\n\nawait client.deleteProfile('user_id', 'profile_id');\n", "label": "JavaScript"}, {"lang": "Go", "source": "// To use the Go SDK, install the package:\n// go get github.com/memodb-io/memobase/src/client/memobase-go@latest\n\nimport (\n    \"github.com/memodb-io/memobase/src/client/memobase-go/core\"\n)\n\nprojectURL := \"YOUR_PROJECT_URL\"\napiKey := \"YOUR_API_KEY\"\nclient, err := core.NewMemoBaseClient(projectURL, apiKey)\nif err != nil {\n    panic(err)\n}\n\n// Get user\nuser, err := client.GetUser(userID)\nif err != nil {\n    panic(err)\n}\n\n// Delete profile\nerr = user.DeleteProfile(profileID)\nif err != nil {\n    panic(err)\n}\n", "label": "Go"}]}}, "/api/v1/users/buffer/{user_id}/{buffer_type}": {"post": {"tags": ["buffer"], "summary": "<PERSON><PERSON><PERSON> Buffer", "description": "Get the real-time user profiles for long term memory", "operationId": "flush_buffer_api_v1_users_buffer__user_id___buffer_type__post", "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "string", "description": "The ID of the user", "title": "User Id"}, "description": "The ID of the user"}, {"name": "buffer_type", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/BlobType", "description": "The type of buffer to flush"}, "description": "The type of buffer to flush"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChatModalAPIResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "x-code-samples": [{"lang": "Python", "source": "# To use the Python SDK, install the package:\n# pip install memobase\n\nfrom memobase import Memobase\n\nclient = Memobase(project_url='PROJECT_URL', api_key='PROJECT_TOKEN')\n\nu.flush()\n", "label": "Python"}, {"lang": "JavaScript", "source": "// To use the JavaScript SDK, install the package:\n// npm install @memobase/memobase\n\nimport { MemoBaseClient, BlobType } from '@memobase/memobase';\n\nconst client = new MemoBaseClient(process.env.MEMOBASE_PROJECT_URL, process.env.MEMOBASE_API_KEY);\nconst user = await client.getUser(userId);\n\nawait user.flush(BlobType.Enum.chat);\n", "label": "JavaScript"}, {"lang": "Go", "source": "// To use the Go SDK, install the package:\n// go get github.com/memodb-io/memobase/src/client/memobase-go@latest\n\nimport (\n    \"github.com/memodb-io/memobase/src/client/memobase-go/core\"\n    \"github.com/memodb-io/memobase/src/client/memobase-go/blob\"\n)\n\nprojectURL := \"YOUR_PROJECT_URL\"\napiKey := \"YOUR_API_KEY\"\nclient, err := core.NewMemoBaseClient(projectURL, apiKey)\nif err != nil {\n    panic(err)\n}\n\n// Get user\nuser, err := client.GetUser(userID)\nif err != nil {\n    panic(err)\n}\n\n// Flush buffer\nerr = user.Flush(blob.ChatType)\nif err != nil {\n    panic(err)\n}\n", "label": "Go"}]}}, "/api/v1/users/event/{user_id}": {"get": {"tags": ["event"], "summary": "Get User Events", "operationId": "get_user_events_api_v1_users_event__user_id__get", "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "string", "description": "The ID of the user", "title": "User Id"}, "description": "The ID of the user"}, {"name": "topk", "in": "query", "required": false, "schema": {"type": "integer", "description": "Number of events to retrieve, default is 10", "default": 10, "title": "Topk"}, "description": "Number of events to retrieve, default is 10"}, {"name": "max_token_size", "in": "query", "required": false, "schema": {"type": "integer", "description": "Max token size of returned events", "title": "<PERSON>"}, "description": "Max token size of returned events"}, {"name": "need_summary", "in": "query", "required": false, "schema": {"type": "boolean", "description": "Whether to return events with summaries", "default": false, "title": "Need Summary"}, "description": "Whether to return events with summaries"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserEventsDataResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "x-code-samples": [{"lang": "Python", "source": "# To use the Python SDK, install the package:\n# pip install memobase\n\nfrom memobase import Memobase\n\nclient = Memobase(project_url='PROJECT_URL', api_key='PROJECT_TOKEN')\nu = client.get_user(uid)\n\nevents = u.event(topk=10, max_token_size=1000, need_summary=True)\n", "label": "Python"}, {"lang": "JavaScript", "source": "// To use the JavaScript SDK, install the package:\n// npm install @memobase/memobase\n\nimport { MemoBaseClient } from '@memobase/memobase';\n\nconst client = new MemoBaseClient(process.env.MEMOBASE_PROJECT_URL, process.env.MEMOBASE_API_KEY);\nconst user = await client.getUser(userId);\n\nconst events = await user.event();\n", "label": "JavaScript"}]}}, "/api/v1/users/event/{user_id}/{event_id}": {"put": {"tags": ["event"], "summary": "Update User Event", "operationId": "update_user_event_api_v1_users_event__user_id___event_id__put", "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "string", "description": "The ID of the user", "title": "User Id"}, "description": "The ID of the user"}, {"name": "event_id", "in": "path", "required": true, "schema": {"type": "string", "description": "The ID of the event", "title": "Event Id"}, "description": "The ID of the event"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EventData", "description": "Event data to update"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "x-code-samples": [{"lang": "Python", "source": "# To use the Python SDK, install the package:\n# pip install memobase\n\nfrom memobase import Memobase\n\nclient = Memobase(project_url='PROJECT_URL', api_key='PROJECT_TOKEN')\nuid = client.add_user()\nu = client.get_user(uid)\n# ... insert messages to user\n\nevents = u.event(topk=5)\neid = events[0].id\n\nu.update_event(eid, {\"event_tip\": \"The event is about...\"})\nprint(u.event(topk=1))\n", "label": "Python"}]}, "delete": {"tags": ["event"], "summary": "Delete User Event", "operationId": "delete_user_event_api_v1_users_event__user_id___event_id__delete", "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "string", "description": "The ID of the user", "title": "User Id"}, "description": "The ID of the user"}, {"name": "event_id", "in": "path", "required": true, "schema": {"type": "string", "description": "The ID of the event", "title": "Event Id"}, "description": "The ID of the event"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "x-code-samples": [{"lang": "Python", "source": "# To use the Python SDK, install the package:\n# pip install memobase\n\nfrom memobase import Memobase\n\nclient = Memobase(project_url='PROJECT_URL', api_key='PROJECT_TOKEN')\nuid = client.add_user()\nu = client.get_user(uid)\n# ... insert messages to user\n\nevents = u.event(topk=1)\nprint(events)\n\neid = events[0].id\nu.delete_event(eid)\n\nprint(u.event(topk=1))\n", "label": "Python"}]}}, "/api/v1/users/event/search/{user_id}": {"get": {"tags": ["event"], "summary": "Search User Events", "operationId": "search_user_events_api_v1_users_event_search__user_id__get", "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "string", "description": "The ID of the user", "title": "User Id"}, "description": "The ID of the user"}, {"name": "query", "in": "query", "required": true, "schema": {"type": "string", "description": "The query to search for", "title": "Query"}, "description": "The query to search for"}, {"name": "topk", "in": "query", "required": false, "schema": {"type": "integer", "description": "Number of events to retrieve, default is 10", "default": 10, "title": "Topk"}, "description": "Number of events to retrieve, default is 10"}, {"name": "similarity_threshold", "in": "query", "required": false, "schema": {"type": "number", "description": "Similarity threshold, default is 0.2", "default": 0.2, "title": "Similarity <PERSON><PERSON><PERSON><PERSON>"}, "description": "Similarity threshold, default is 0.2"}, {"name": "time_range_in_days", "in": "query", "required": false, "schema": {"type": "integer", "description": "Time range in days, default is 7", "default": 7, "title": "Time Range In Days"}, "description": "Time range in days, default is 7"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserEventsDataResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "x-code-samples": [{"lang": "Python", "source": "# To use the Python SDK, install the package:\n# pip install memobase\n\nfrom memobase import Memobase\n\nclient = Memobase(project_url='PROJECT_URL', api_key='PROJECT_TOKEN')\nuid = client.add_user()\nu = client.get_user(uid)\n\nb = ChatBlob(messages=[\n    {\n        \"role\": \"user\",\n        \"content\": \"Hi, I'm here again\"\n    },\n    {\n        \"role\": \"assistant\",\n        \"content\": \"<PERSON>, <PERSON>! How can I help you?\"\n    }\n])\nu.insert(b)\nu.flush()\n\nevents = u.search_event('query')\nprint(events)\n", "label": "Python"}]}}, "/api/v1/users/context/{user_id}": {"get": {"tags": ["context"], "summary": "Get User Context", "operationId": "get_user_context_api_v1_users_context__user_id__get", "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "string", "description": "The ID of the user", "title": "User Id"}, "description": "The ID of the user"}, {"name": "max_token_size", "in": "query", "required": false, "schema": {"type": "integer", "description": "Max token size of returned Context", "default": 1000, "title": "<PERSON>"}, "description": "Max token size of returned Context"}, {"name": "prefer_topics", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}, "description": "Rank prefer topics at first to try to keep them in filtering, default order is by updated time", "title": "Prefer Topics"}, "description": "Rank prefer topics at first to try to keep them in filtering, default order is by updated time"}, {"name": "only_topics", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}, "description": "Only return profiles with these topics, default is all", "title": "Only Topics"}, "description": "Only return profiles with these topics, default is all"}, {"name": "max_subtopic_size", "in": "query", "required": false, "schema": {"type": "integer", "description": "Max subtopic size of the same topic in returned Context", "title": "Max Subtopic Size"}, "description": "Max subtopic size of the same topic in returned Context"}, {"name": "topic_limits_json", "in": "query", "required": false, "schema": {"type": "string", "description": "Set specific subtopic limits for topics in JSON, for example {\"topic1\": 3, \"topic2\": 5}. The limits in this param will override `max_subtopic_size`.", "title": "Topic Limits Json"}, "description": "Set specific subtopic limits for topics in JSON, for example {\"topic1\": 3, \"topic2\": 5}. The limits in this param will override `max_subtopic_size`."}, {"name": "profile_event_ratio", "in": "query", "required": false, "schema": {"type": "number", "description": "Profile event ratio of returned Context", "default": 0.6, "title": "Profile Event Ratio"}, "description": "Profile event ratio of returned Context"}, {"name": "require_event_summary", "in": "query", "required": false, "schema": {"type": "boolean", "description": "Whether to require event summary in returned Context", "default": false, "title": "Require Event Summary"}, "description": "Whether to require event summary in returned Context"}, {"name": "chats_str", "in": "query", "required": false, "schema": {"type": "string", "description": "List of chats in OpenAI Message format, for example: [{\"role\": \"user\", \"content\": \"Hello\"}, {\"role\": \"assistant\", \"content\": \"Hi\"}]", "title": "Chats Str"}, "description": "List of chats in OpenAI Message format, for example: [{\"role\": \"user\", \"content\": \"Hello\"}, {\"role\": \"assistant\", \"content\": \"Hi\"}]"}, {"name": "event_similarity_threshold", "in": "query", "required": false, "schema": {"type": "number", "description": "Event similarity threshold of returned Context", "default": 0.2, "title": "Event Similarity Threshold"}, "description": "Event similarity threshold of returned Context"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserContextDataResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "x-code-samples": [{"lang": "Python", "source": "# To use the Python SDK, install the package:\n# pip install memobase\n\nfrom memobase import Memobase\n\nclient = Memobase(project_url='PROJECT_URL', api_key='PROJECT_TOKEN')\n\ncontext = u.context()\n", "label": "Python"}, {"lang": "JavaScript", "source": "// To use the JavaScript SDK, install the package:\n// npm install @memobase/memobase\n\nimport { MemoBaseClient } from '@memobase/memobase';\n\nconst client = new MemoBaseClient(process.env.MEMOBASE_PROJECT_URL, process.env.MEMOBASE_API_KEY);\nconst user = await client.getUser(userId);\n\nconst context = await user.context();\n", "label": "JavaScript"}]}}}, "components": {"schemas": {"BaseResponse": {"properties": {"data": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Data", "description": "Response data payload"}, "errno": {"$ref": "#/components/schemas/CODE", "description": "Error code, 0 means success", "default": 0}, "errmsg": {"type": "string", "title": "Errmsg", "description": "Error message, empty when success", "default": ""}}, "type": "object", "title": "BaseResponse"}, "BillingData": {"properties": {"token_left": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Token Left", "description": "Total token left"}, "next_refill_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Next Refill At", "description": "Next refill time"}, "project_token_cost_month": {"type": "integer", "title": "Project Token Cost Month", "description": "Token cost of this project for this month"}}, "type": "object", "required": ["project_token_cost_month"], "title": "BillingData"}, "BillingResponse": {"properties": {"data": {"anyOf": [{"$ref": "#/components/schemas/BillingData"}, {"type": "null"}], "description": "Response containing token left"}, "errno": {"$ref": "#/components/schemas/CODE", "description": "Error code, 0 means success", "default": 0}, "errmsg": {"type": "string", "title": "Errmsg", "description": "Error message, empty when success", "default": ""}}, "type": "object", "title": "BillingResponse"}, "BlobData": {"properties": {"blob_type": {"$ref": "#/components/schemas/BlobType"}, "blob_data": {"additionalProperties": true, "type": "object", "title": "Blob Data"}, "fields": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Fields"}, "created_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Created At"}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At"}}, "type": "object", "required": ["blob_type", "blob_data"], "title": "BlobData"}, "BlobDataResponse": {"properties": {"data": {"anyOf": [{"$ref": "#/components/schemas/BlobData"}, {"type": "null"}], "description": "Response containing blob data"}, "errno": {"$ref": "#/components/schemas/CODE", "description": "Error code, 0 means success", "default": 0}, "errmsg": {"type": "string", "title": "Errmsg", "description": "Error message, empty when success", "default": ""}}, "type": "object", "title": "BlobDataResponse"}, "BlobInsertData": {"properties": {"id": {"anyOf": [{"type": "string", "format": "uuid4"}, {"type": "string", "format": "uuid5"}], "title": "Id", "description": "The UUID identifier"}, "chat_results": {"anyOf": [{"items": {"$ref": "#/components/schemas/ChatModalResponse"}, "type": "array"}, {"type": "null"}], "title": "Chat Results", "description": "List of chat modal data"}}, "type": "object", "required": ["id"], "title": "BlobInsertData"}, "BlobInsertResponse": {"properties": {"data": {"anyOf": [{"$ref": "#/components/schemas/BlobInsertData"}, {"type": "null"}], "description": "Response containing blob insert data"}, "errno": {"$ref": "#/components/schemas/CODE", "description": "Error code, 0 means success", "default": 0}, "errmsg": {"type": "string", "title": "Errmsg", "description": "Error message, empty when success", "default": ""}}, "type": "object", "title": "BlobInsertResponse"}, "BlobType": {"type": "string", "enum": ["chat", "doc", "image", "code", "transcript"], "title": "BlobType"}, "CODE": {"type": "integer", "enum": [0, 400, 401, 403, 404, 405, 409, 422, 500, 501, 502, 503, 504, 520], "title": "CODE"}, "ChatModalAPIResponse": {"properties": {"data": {"anyOf": [{"items": {"$ref": "#/components/schemas/ChatModalResponse"}, "type": "array"}, {"type": "null"}], "title": "Data", "description": "Response containing chat modal data"}, "errno": {"$ref": "#/components/schemas/CODE", "description": "Error code, 0 means success", "default": 0}, "errmsg": {"type": "string", "title": "Errmsg", "description": "Error message, empty when success", "default": ""}}, "type": "object", "title": "ChatModalAPIResponse"}, "ChatModalResponse": {"properties": {"event_id": {"anyOf": [{"type": "string", "format": "uuid4"}, {"type": "string", "format": "uuid5"}, {"type": "null"}], "title": "Event Id", "description": "The event's unique identifier"}, "add_profiles": {"anyOf": [{"items": {"anyOf": [{"type": "string", "format": "uuid4"}, {"type": "string", "format": "uuid5"}]}, "type": "array"}, {"type": "null"}], "title": "Add Profiles", "description": "List of added profiles' ids"}, "update_profiles": {"anyOf": [{"items": {"anyOf": [{"type": "string", "format": "uuid4"}, {"type": "string", "format": "uuid5"}]}, "type": "array"}, {"type": "null"}], "title": "Update Profiles", "description": "List of updated profiles' ids"}, "delete_profiles": {"anyOf": [{"items": {"anyOf": [{"type": "string", "format": "uuid4"}, {"type": "string", "format": "uuid5"}]}, "type": "array"}, {"type": "null"}], "title": "Delete Profiles", "description": "List of deleted profiles' ids"}}, "type": "object", "required": ["event_id", "add_profiles", "update_profiles", "delete_profiles"], "title": "ChatModalResponse"}, "ContextData": {"properties": {"context": {"type": "string", "title": "Context", "description": "Context string"}}, "type": "object", "required": ["context"], "title": "ContextData"}, "EventData": {"properties": {"profile_delta": {"anyOf": [{"items": {"$ref": "#/components/schemas/ProfileDelta"}, "type": "array"}, {"type": "null"}], "title": "Profile Delta", "description": "List of profile data"}, "event_tip": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Event Tip", "description": "Event tip"}, "event_tags": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventTag"}, "type": "array"}, {"type": "null"}], "title": "Event Tags", "description": "List of event tags"}}, "type": "object", "title": "EventData"}, "EventTag": {"properties": {"tag": {"type": "string", "title": "Tag", "description": "The event tag"}, "value": {"type": "string", "title": "Value", "description": "The event tag value"}}, "type": "object", "required": ["tag", "value"], "title": "EventTag"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "IdData": {"properties": {"id": {"anyOf": [{"type": "string", "format": "uuid4"}, {"type": "string", "format": "uuid5"}], "title": "Id", "description": "The UUID identifier"}}, "type": "object", "required": ["id"], "title": "IdData"}, "IdResponse": {"properties": {"data": {"anyOf": [{"$ref": "#/components/schemas/IdData"}, {"type": "null"}], "description": "Response containing a single ID"}, "errno": {"$ref": "#/components/schemas/CODE", "description": "Error code, 0 means success", "default": 0}, "errmsg": {"type": "string", "title": "Errmsg", "description": "Error message, empty when success", "default": ""}}, "type": "object", "title": "IdResponse"}, "IdsData": {"properties": {"ids": {"items": {"anyOf": [{"type": "string", "format": "uuid4"}, {"type": "string", "format": "uuid5"}]}, "type": "array", "title": "Ids", "description": "List of UUID identifiers"}}, "type": "object", "required": ["ids"], "title": "IdsData"}, "IdsResponse": {"properties": {"data": {"anyOf": [{"$ref": "#/components/schemas/IdsData"}, {"type": "null"}], "description": "Response containing multiple IDs"}, "errno": {"$ref": "#/components/schemas/CODE", "description": "Error code, 0 means success", "default": 0}, "errmsg": {"type": "string", "title": "Errmsg", "description": "Error message, empty when success", "default": ""}}, "type": "object", "title": "IdsResponse"}, "ProfileConfigData": {"properties": {"profile_config": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Profile Config", "description": "Profile config string"}}, "type": "object", "required": ["profile_config"], "title": "ProfileConfigData"}, "ProfileConfigDataResponse": {"properties": {"data": {"anyOf": [{"$ref": "#/components/schemas/ProfileConfigData"}, {"type": "null"}], "description": "Response containing profile config data"}, "errno": {"$ref": "#/components/schemas/CODE", "description": "Error code, 0 means success", "default": 0}, "errmsg": {"type": "string", "title": "Errmsg", "description": "Error message, empty when success", "default": ""}}, "type": "object", "title": "ProfileConfigDataResponse"}, "ProfileData": {"properties": {"id": {"anyOf": [{"type": "string", "format": "uuid4"}, {"type": "string", "format": "uuid5"}], "title": "Id", "description": "The profile's unique identifier"}, "content": {"type": "string", "title": "Content", "description": "User profile content value"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "Timestamp when the profile was created"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At", "description": "Timestamp when the profile was last updated"}, "attributes": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Attributes", "description": "User profile attributes in JSON, containing 'topic', 'sub_topic'"}}, "type": "object", "required": ["id", "content"], "title": "ProfileData"}, "ProfileDelta": {"properties": {"content": {"type": "string", "title": "Content", "description": "The profile content"}, "attributes": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Attributes", "description": "User profile attributes in JSON, containing 'topic', 'sub_topic'"}}, "type": "object", "required": ["content", "attributes"], "title": "ProfileDelta"}, "UserContextDataResponse": {"properties": {"data": {"anyOf": [{"$ref": "#/components/schemas/ContextData"}, {"type": "null"}], "description": "Response containing user context"}, "errno": {"$ref": "#/components/schemas/CODE", "description": "Error code, 0 means success", "default": 0}, "errmsg": {"type": "string", "title": "Errmsg", "description": "Error message, empty when success", "default": ""}}, "type": "object", "title": "UserContextDataResponse"}, "UserContextImport": {"properties": {"context": {"type": "string", "title": "Context", "description": "The user context you want to import to Memobase"}}, "type": "object", "required": ["context"], "title": "UserContextImport"}, "UserData": {"properties": {"data": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Data", "description": "User additional data in JSON"}, "id": {"anyOf": [{"type": "string", "format": "uuid4"}, {"type": "string", "format": "uuid5"}, {"type": "null"}], "title": "Id", "description": "User ID in UUIDv4/5"}, "created_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Created At", "description": "Timestamp when the user was created"}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At", "description": "Timestamp when the user was last updated"}}, "type": "object", "title": "UserData"}, "UserDataResponse": {"properties": {"data": {"anyOf": [{"$ref": "#/components/schemas/UserData"}, {"type": "null"}], "description": "Response containing user data"}, "errno": {"$ref": "#/components/schemas/CODE", "description": "Error code, 0 means success", "default": 0}, "errmsg": {"type": "string", "title": "Errmsg", "description": "Error message, empty when success", "default": ""}}, "type": "object", "title": "UserDataResponse"}, "UserEventData": {"properties": {"id": {"anyOf": [{"type": "string", "format": "uuid4"}, {"type": "string", "format": "uuid5"}], "title": "Id", "description": "The event's unique identifier"}, "event_data": {"$ref": "#/components/schemas/EventData", "description": "User event data in JSON"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "Timestamp when the event was created"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At", "description": "Timestamp when the event was last updated"}, "similarity": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Similarity", "description": "Similarity score"}}, "type": "object", "required": ["id"], "title": "UserEventData"}, "UserEventsData": {"properties": {"events": {"items": {"$ref": "#/components/schemas/UserEventData"}, "type": "array", "title": "Events", "description": "List of user events"}}, "type": "object", "required": ["events"], "title": "UserEventsData"}, "UserEventsDataResponse": {"properties": {"data": {"anyOf": [{"$ref": "#/components/schemas/UserEventsData"}, {"type": "null"}], "description": "Response containing user events"}, "errno": {"$ref": "#/components/schemas/CODE", "description": "Error code, 0 means success", "default": 0}, "errmsg": {"type": "string", "title": "Errmsg", "description": "Error message, empty when success", "default": ""}}, "type": "object", "title": "UserEventsDataResponse"}, "UserProfileResponse": {"properties": {"data": {"anyOf": [{"$ref": "#/components/schemas/UserProfilesData"}, {"type": "null"}], "description": "Response containing user profiles"}, "errno": {"$ref": "#/components/schemas/CODE", "description": "Error code, 0 means success", "default": 0}, "errmsg": {"type": "string", "title": "Errmsg", "description": "Error message, empty when success", "default": ""}}, "type": "object", "title": "UserProfileResponse"}, "UserProfilesData": {"properties": {"profiles": {"items": {"$ref": "#/components/schemas/ProfileData"}, "type": "array", "title": "Profiles", "description": "List of user profiles"}}, "type": "object", "required": ["profiles"], "title": "UserProfilesData"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}}, "securitySchemes": {"BearerAuth": {"type": "http", "scheme": "bearer"}}}, "security": [{"BearerAuth": []}]}